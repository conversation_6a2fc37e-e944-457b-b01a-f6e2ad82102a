import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { ThemeProvider } from 'styled-components';
import Layout from './components/layout/Layout';
import Home from './pages/Home';
import About from './pages/About';
import Services from './pages/Services';
import Pricing from './pages/Pricing';
import Contact from './pages/Contact';
import NotFound from './pages/NotFound';
import DevSettings from './pages/DevSettings';
import theme from './styles/theme';
import GlobalStyles from './styles/globalStyles';
import { AppContent } from './AppContent';

function App() {
  return (
    <ThemeProvider theme={theme}>
      <GlobalStyles />
      <Router>
        <AppContent>
          <Layout>
            <Routes>
              <Route path="/" element={<Home />} />
              <Route path="/about" element={<About />} />
              <Route path="/services" element={<Services />} />
              <Route path="/pricing" element={<Pricing />} />
              <Route path="/contact" element={<Contact />} />
              <Route path="/dev-settings" element={<DevSettings />} />
              <Route path="*" element={<NotFound />} />
            </Routes>
          </Layout>
        </AppContent>
      </Router>
    </ThemeProvider>
  );
}

export default App;
